#!/usr/bin/env python
"""
Example training a model from config

This script demonstrates:
1. Builds a model from a config
2. Prepares data from config
3. Runs training with specified arguments
4. Registers the model in a registry for easy loading

Usage:
    python model_train.py --config configs/v1_cones_dense_gaussian_standard_mish.yaml --test

Optional arguments

"""
#%%
import torch
from torch.utils.data import DataLoader
import argparse
import sys
import time

import lightning as pl
from lightning.pytorch.loggers import WandbLogger
from lightning.pytorch.profilers import PyTorchProfiler

from pathlib import Path
import numpy as np
import os
from DataYatesV1.models import build_model, initialize_model_components, get_name_from_config
from DataYatesV1.models.config_loader import load_config
from DataYatesV1.models.lightning import PLCoreVisionModel
from DataYatesV1.models.model_manager import ModelRegistry
from DataYatesV1.utils.data import prepare_data
from DataYatesV1.utils.general import ensure_tensor
from DataYatesV1.utils.torch import get_free_device
import yaml
from DataYatesV1.models.utils.general import ValidateOnTrainStart
import sys

# Set random seed for reproducibility
torch.manual_seed(42)
np.random.seed(42)

registry_dir = Path("/mnt/ssd/YatesMarmoV1/conv_model_fits/model_registry")
registry = ModelRegistry(registry_dir)

#%%
def create_dataloaders(train_dset, val_dset, batch_size=256):
    """Create DataLoader objects for training."""
    train_loader = DataLoader(
        train_dset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=os.cpu_count()-4,
        pin_memory=True
    )

    val_loader = DataLoader(
        val_dset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=os.cpu_count()-4,
        pin_memory=True
    )

    return train_loader, val_loader


class CompileSubmodules(pl.Callback):
    """
    Compile convnet and recurrent once the model is on GPU.
    Attach to Trainer callbacks list.
    """
    def __init__(self, convnet=True, recurrent=True, compile_kwargs=None):
        self.convnet = convnet
        self.recurrent = recurrent
        self.kw = compile_kwargs or dict(mode="reduce-overhead")
        self._done = False

    def on_fit_start(self, trainer, pl_module):
        if self._done:       # only once, even if you resume training
            return

        m = pl_module.model   # your assembled core model

        if self.convnet:
            m.convnet = torch.compile(m.convnet, **self.kw)

        if self.recurrent and hasattr(m, "recurrent"):
            m.recurrent = torch.compile(m.recurrent, **self.kw)

        self._done = True
        rank = trainer.global_rank if hasattr(trainer, "global_rank") else 0
        if rank == 0:
            print("[torch.compile] convnet / recurrent compiled")


def train_model(config, train_loader, val_loader, dataset_config, checkpoint_dir, dataset_config_path, device=None, max_epochs=2, compile=False, test_mode=False, early_stopping_patience=5):
    """Train a model and save checkpoints.

    Args:
        config: Model configuration dictionary
        train_loader: Training data loader
        val_loader: Validation data loader
        dataset_config: Dataset configuration
        checkpoint_dir: Directory to save checkpoints
        dataset_config_path: Path to the dataset configuration file
        device: Device specification (string or torch.device). Can be 'auto', 'cpu', 'cuda', 'cuda:0', etc.
        max_epochs: Maximum number of epochs to train
        compile: Whether to compile the model with torch.compile
        test_mode: If True, limit batches and enable profiler for testing
        early_stopping_patience: Number of epochs with no improvement after which training will be stopped
    """
    model_name = get_name_from_config(config)

    # Handle device specification
    if device is None or device == 'auto':
        device = get_free_device()
        print(f"Auto-selected device: {device}")
    elif isinstance(device, str):
        if device.lower() == 'cpu':
            device = torch.device('cpu')
        elif device.lower().startswith('cuda'):
            device = torch.device(device)
        else:
            raise ValueError(f"Invalid device specification: {device}")
        print(f"Using specified device: {device}")
    elif isinstance(device, torch.device):
        print(f"Using device: {device}")
    else:
        raise ValueError(f"Invalid device type: {type(device)}")

    # Determine accelerator and devices for PyTorch Lightning
    if device.type == 'cpu':
        accelerator = 'cpu'
        devices = 'auto'  # Let Lightning handle CPU device selection
    elif device.type == 'cuda':
        accelerator = 'gpu'
        devices = [device.index] if device.index is not None else 'auto'
    else:
        # Handle other device types (mps, etc.)
        accelerator = 'auto'
        devices = 'auto'

    # Create the trainer with checkpoint and early stopping callbacks
    checkpoint_callback = pl.pytorch.callbacks.ModelCheckpoint(
        dirpath=str(checkpoint_dir),
        filename=model_name+'-{epoch:02d}-{val_loss:.4f}',
        monitor='val_loss',
        mode='min',
        save_top_k=2,
        save_last=True
    )

    # Add early stopping callback to prevent overfitting
    early_stopping_callback = pl.pytorch.callbacks.EarlyStopping(
        monitor='val_loss',
        patience=early_stopping_patience,
        verbose=True,
        mode='min'
    )

    callbacks = [checkpoint_callback, early_stopping_callback, ValidateOnTrainStart()]

    # check if in interactive notebook. if not. compile model for speedup
    if (not 'ipykernel' in sys.modules) and compile:
        print("Adding compile callback to trainer...")
        compile_cb = CompileSubmodules(
        convnet=True,
        recurrent=True,
        compile_kwargs=dict(fullgraph=False, dynamic=True, mode="default")
        )
        callbacks.append(compile_cb)

    # Configure profiler if in test mode
    profiler = None
    if test_mode:
        print("Test mode enabled: Using PyTorch profiler")
        profiler = PyTorchProfiler(
            dirpath=str(checkpoint_dir),
            filename="profiler-traces",
            schedule=torch.profiler.schedule(
                wait=1,
                warmup=1,
                active=3,
            ),
            record_shapes=True,
            profile_memory=True,
            with_stack=True
        )
    else:
        profiler = "simple"  # Use simple profiler in non-test mode

    print("\nCalculating baseline firing rates...")
    fr = 0
    n = 0
    for dset in train_loader.dataset.dsets:
        fr += dset.covariates['robs'].sum(0)
        n += dset.covariates['robs'].shape[0]
    baseline_rates = fr / n
    inv_softplus = lambda x, beta=1: torch.log(torch.exp(beta*x) - 1) / beta

    # Create Lightning module
    pl_model = PLCoreVisionModel(
        model_class=build_model,  # Pass the factory function
        model_config=config,      # Pass the configuration
        optimizer='AdamW',
        optim_kwargs={'lr': 5e-4, 'weight_decay': 1e-5},
        accumulate_grad_batches=1,
        dataset_info=dataset_config  # Pass dataset information
    )

    # Configure trainer parameters
    trainer_kwargs = {
        "callbacks": callbacks,
        "accelerator": accelerator,
        "devices": devices,
        "logger": WandbLogger(
            project='Digital Twin',
            name=model_name,
            save_code=True,
            entity='yateslab',
            save_dir=str(checkpoint_dir),
        ),
        "default_root_dir": str(checkpoint_dir),
        "max_epochs": max_epochs if not test_mode else 1,  # Limit epochs in test mode
        "check_val_every_n_epoch": 1,
        "profiler": profiler,
    }

    # Add batch limits if in test mode
    if test_mode:
        trainer_kwargs.update({
            "limit_train_batches": 5,
            "limit_val_batches": 3,
        })

    # initialize model
    initialize_model_components(pl_model.model, init_bias=inv_softplus(baseline_rates))
    # pl_model.model.readout.bias.data = ensure_tensor(inv_softplus(baseline_rates), device=pl_model.model.readout.bias.device, dtype=pl_model.model.readout.bias.dtype)

    # Create trainer and train
    trainer = pl.Trainer(**trainer_kwargs)

    # Start timing
    start_time = time.time()

    # Train the model
    trainer.fit(pl_model, train_loader, val_loader)

    # End timing
    end_time = time.time()
    training_time = end_time - start_time

    # Get the best model path
    best_model_path = checkpoint_callback.best_model_path

    # Print training summary and profiler information
    print("\n===== TRAINING SUMMARY =====")
    print(f"Total training time: {training_time:.2f} seconds")

    # Print metrics
    print("\nTraining metrics:")
    for metric_name, metric_value in trainer.callback_metrics.items():
        if isinstance(metric_value, torch.Tensor):
            print(f"- {metric_name}: {metric_value.item():.6f}")
        else:
            print(f"- {metric_name}: {metric_value}")

    # Print additional profiler information if in test mode
    if test_mode and isinstance(profiler, PyTorchProfiler):
        print("\n===== PROFILER INFORMATION =====")

        # Print profiler output location
        profiler_output = os.path.join(checkpoint_dir, "profiler-traces")
        print(f"Detailed profiler traces saved to: {profiler_output}")
        print("You can view these traces with TensorBoard or Chrome trace viewer")

        # Print model performance summary
        print("\nModel compilation status:")
        if compile:
            print("- Model was compiled with torch.compile")
            print("- Convnet and recurrent modules were compiled")
        else:
            print("- Model was NOT compiled (running in eager mode)")

    # Print model architecture summary
    print("\nModel architecture summary:")
    print(f"- Frontend: {config['frontend']['type']}")
    print(f"- Convnet: {config['convnet']['type']}")
    print(f"- Recurrent: {config.get('recurrent', {}).get('type', 'None')}")
    print(f"- Readout: {config['readout']['type']}")

    # Print training configuration
    print("\nTraining configuration:")
    print(f"- Early stopping: Enabled (patience={early_stopping_patience})")
    print(f"- Max epochs: {max_epochs}")
    print(f"- Model compilation: {compile}")
    print("============================")

    # Register the model
    model_id = f"{model_name}_epoch_{trainer.current_epoch}"

    registry.register_model(
        model_id=model_id,
        checkpoint_path=best_model_path,
        config_path=config_path,
        metrics={'val_loss': trainer.callback_metrics.get('val_loss', 0).item()},
        metadata={
            'epochs': trainer.current_epoch,
            'training_time': training_time,
            'batch_size': train_loader.batch_size,
            'compiled': compile
        },
        dataset_config_path=dataset_config_path
    )

    return best_model_path

#%% Main execution
def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description='Train and test neural network models')

    parser.add_argument('--config', type=str,
                        default='/mnt/ssd/YatesMarmoV1/conv_model_fits/configs/v1_cones_dense_gaussian_standard_mish.yaml',
                        help='Path to model configuration file')

    parser.add_argument('--dataset-config', type=str,
                        default='/mnt/ssd/YatesMarmoV1/conv_model_fits/configs/dataset_config.yaml',
                        help='Path to dataset configuration file')

    parser.add_argument('--compile', action='store_true',
                        help='Enable model compilation with torch.compile')

    parser.add_argument('--test', action='store_true',
                        help='Run in test mode with limited batches and profiling')

    parser.add_argument('--max-epochs', type=int, default=25,
                        help='Maximum number of training epochs')

    parser.add_argument('--batch-size', type=int, default=256,
                        help='Batch size for training and validation')

    parser.add_argument('--early-stopping-patience', type=int, default=5,
                        help='Number of epochs with no improvement after which training will be stopped')

    parser.add_argument('--device', type=str, default='auto',
                        help='Device to use for training. Options: "auto", "cpu", "cuda", "cuda:0", etc. Default: "auto" (automatically select best available device)')

    return parser.parse_args()

if __name__ == "__main__":
    # Parse command-line arguments
    args = parse_arguments()

    # Set paths based on arguments
    config_path = Path(args.config)
    dataset_config_path = Path(args.dataset_config)

    print(f"Using config: {config_path}")
    print(f"Using dataset config: {dataset_config_path}")
    print(f"Device: {args.device}")
    print(f"Compilation enabled: {args.compile}")
    print(f"Test mode: {args.test}")
    print(f"Early stopping patience: {args.early_stopping_patience}")

    # Load dataset configuration
    with open(dataset_config_path, 'r') as f:
        dataset_config = yaml.safe_load(f)

    # Prepare data using the dataset configuration
    train_dset, val_dset, dataset_config = prepare_data(dataset_config)
    train_loader, val_loader = create_dataloaders(train_dset, val_dset, batch_size=args.batch_size)

    # Update config with number of units
    config = load_config(config_path)
    config['readout']['params']['n_units'] = len(dataset_config['cids'])

    checkpoint_dir = Path(f"/mnt/ssd/YatesMarmoV1/conv_model_fits/runs/{get_name_from_config(config)}")
    checkpoint_dir.mkdir(parents=True, exist_ok=True)

    # Train the model
    best_model_path = train_model(
        config=config,
        train_loader=train_loader,
        val_loader=val_loader,
        dataset_config=dataset_config,
        checkpoint_dir=checkpoint_dir,
        dataset_config_path=dataset_config_path,
        device=args.device,
        max_epochs=args.max_epochs,
        compile=args.compile,
        test_mode=args.test,
        early_stopping_patience=args.early_stopping_patience
    )

    # Find and load the best model from the registry
    best_model, model_entry = registry.get_best_model(
        metric='val_loss',
        mode='min',
        config_match={'model_type': 'v1'},
        dataset_match={'session': dataset_config['session'], 'types': ['gaborium', 'backimage']}
    )

    print("\nDone!")