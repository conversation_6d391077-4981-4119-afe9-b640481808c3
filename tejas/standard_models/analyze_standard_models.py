#%%
import os
import json
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from typing import Dict, List, Tuple, Optional
from pathlib import Path
import warnings

# Add the DataYatesV1 module imports
from DataYatesV1 import get_complete_sessions, get_session

class StandardModelAnalyzer:
    """
    Analyzer for standard model results comparing LN and Energy models.
    """
    
    def __init__(self, base_dir: str = "/mnt/ssd/YatesMarmoV1/standard_model_fits"):
        """
        Initialize the analyzer.
        
        Args:
            base_dir: Base directory where model results are stored
        """
        self.base_dir = Path(base_dir)
        
    def get_model_directory_pattern(self, model_type: str) -> str:
        """
        Get the directory pattern for a specific model type.
        
        Args:
            model_type: Either 'ln' or 'energy'
            
        Returns:
            Directory pattern string
        """
        # Based on the code, the directory pattern is:
        # ray_{model_type}_sweep_6040_30trials_noleak_only_fixations3
        return f"ray_{model_type}_sweep_6040_30trials_noleak_only_fixations3"
    
    def load_cell_results(self, session_name: str, model_type: str, cell_id: int) -> Optional[Dict]:
        """
        Load results for a specific cell from a specific model type.
        
        Args:
            session_name: Name of the session (e.g., 'Allen_2022-04-13')
            model_type: Either 'ln' or 'energy' 
            cell_id: ID of the cell
            
        Returns:
            Dictionary with results or None if not found
        """
        model_dir_pattern = self.get_model_directory_pattern(model_type)
        results_path = self.base_dir / model_dir_pattern / session_name / f"cell_{cell_id}_best" / "results.json"
        
        if not results_path.exists():
            return None
            
        try:
            with open(results_path, 'r') as f:
                results = json.load(f)
            return results
        except (json.JSONDecodeError, FileNotFoundError) as e:
            warnings.warn(f"Could not load results for {session_name}, {model_type}, cell {cell_id}: {e}")
            return None
    
    def get_valid_cells_for_session(self, session_name: str, model_type: str) -> List[int]:
        """
        Get list of valid cell IDs for a session and model type.
        
        Args:
            session_name: Name of the session
            model_type: Either 'ln' or 'energy'
            
        Returns:
            List of valid cell IDs
        """
        model_dir_pattern = self.get_model_directory_pattern(model_type)
        session_dir = self.base_dir / model_dir_pattern / session_name
        
        if not session_dir.exists():
            return []
        
        valid_cells = []
        for cell_dir in session_dir.iterdir():
            if cell_dir.is_dir() and cell_dir.name.startswith("cell_") and cell_dir.name.endswith("_best"):
                # Extract cell ID from directory name
                try:
                    cell_id = int(cell_dir.name.split("_")[1])
                    results_path = cell_dir / "results.json"
                    if results_path.exists():
                        # Verify the results.json is valid
                        results = self.load_cell_results(session_name, model_type, cell_id)
                        if results is not None and 'val_bps' in results:
                            valid_cells.append(cell_id)
                except (ValueError, IndexError):
                    continue
        
        return sorted(valid_cells)
    
    def get_intersecting_cells(self, session_name: str) -> List[int]:
        """
        Get cells that have valid results for both LN and energy models.
        
        Args:
            session_name: Name of the session
            
        Returns:
            List of cell IDs that have results for both models
        """
        ln_cells = set(self.get_valid_cells_for_session(session_name, 'ln'))
        energy_cells = set(self.get_valid_cells_for_session(session_name, 'energy'))
        
        intersecting_cells = list(ln_cells.intersection(energy_cells))
        return sorted(intersecting_cells)
    
    def load_session_comparison_data(self, session_name: str) -> pd.DataFrame:
        """
        Load comparison data for a session.
        
        Args:
            session_name: Name of the session
            
        Returns:
            DataFrame with comparison data
        """
        intersecting_cells = self.get_intersecting_cells(session_name)
        
        if not intersecting_cells:
            return pd.DataFrame()
        
        data = []
        for cell_id in intersecting_cells:
            ln_results = self.load_cell_results(session_name, 'ln', cell_id)
            energy_results = self.load_cell_results(session_name, 'energy', cell_id)
            
            if ln_results is not None and energy_results is not None:
                data.append({
                    'session': session_name,
                    'cell_id': cell_id,
                    'ln_val_bps': ln_results['val_bps'],
                    'energy_val_bps': energy_results['val_bps'],
                    'ln_train_bps': ln_results.get('train_bps', np.nan),
                    'energy_train_bps': energy_results.get('train_bps', np.nan),
                    'ln_initial_val_bps': ln_results.get('intial_val_bps', np.nan),  # Note: typo in original
                    'energy_initial_val_bps': energy_results.get('intial_val_bps', np.nan),
                })
        
        return pd.DataFrame(data)
    
    def load_all_sessions_data(self, session_names: Optional[List[str]] = None) -> pd.DataFrame:
        """
        Load comparison data for all specified sessions.
        
        Args:
            session_names: List of session names. If None, will try to find all available sessions.
            
        Returns:
            DataFrame with all session comparison data
        """
        if session_names is None:
            # Try to find all available sessions by looking at the directory structure
            session_names = self.get_available_sessions()
        
        all_data = []
        for session_name in session_names:
            session_data = self.load_session_comparison_data(session_name)
            if not session_data.empty:
                all_data.append(session_data)
        
        if all_data:
            return pd.concat(all_data, ignore_index=True)
        else:
            return pd.DataFrame()
    
    def get_available_sessions(self) -> List[str]:
        """
        Get list of available sessions by looking at the directory structure.
        
        Returns:
            List of available session names
        """
        sessions = set()
        
        # Check both ln and energy directories
        for model_type in ['ln', 'energy']:
            model_dir_pattern = self.get_model_directory_pattern(model_type)
            model_dir = self.base_dir / model_dir_pattern
            
            if model_dir.exists():
                for session_dir in model_dir.iterdir():
                    if session_dir.is_dir():
                        sessions.add(session_dir.name)
        
        return sorted(list(sessions))
    
    def plot_model_comparison(self, data: pd.DataFrame, 
                            session_name: Optional[str] = None,
                            save_path: Optional[str] = None,
                            figsize: Tuple[int, int] = (10, 8)) -> plt.Figure:
        """
        Create a scatter plot comparing LN and Energy model validation BPS.
        
        Args:
            data: DataFrame with comparison data
            session_name: Optional session name for title
            save_path: Optional path to save the figure
            figsize: Figure size tuple
            
        Returns:
            matplotlib Figure object
        """
        if data.empty:
            raise ValueError("No data provided for plotting")
        
        fig, ax = plt.subplots(figsize=figsize)
        
        # Create scatter plot
        scatter = ax.scatter(data['ln_val_bps'], data['energy_val_bps'], 
                           alpha=0.7, s=50, edgecolors='black', linewidth=0.5)
        
        # Add cell ID annotations
        for i, row in data.iterrows():
            ax.annotate(str(row['cell_id']), 
                       (row['ln_val_bps'], row['energy_val_bps']),
                       xytext=(5, 5), textcoords='offset points',
                       fontsize=8, alpha=0.8)
        
        # Add diagonal line (y=x) for reference
        min_val = min(data['ln_val_bps'].min(), data['energy_val_bps'].min())
        max_val = max(data['ln_val_bps'].max(), data['energy_val_bps'].max())
        ax.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, linewidth=2, label='y=x')
        
        # Set labels and title
        ax.set_xlabel('LN Model Validation BPS', fontsize=12)
        ax.set_ylabel('Energy Model Validation BPS', fontsize=12)
        
        if session_name:
            ax.set_title(f'LN vs Energy Model Comparison - {session_name}', fontsize=14)
        else:
            ax.set_title('LN vs Energy Model Comparison', fontsize=14)
        
        # Add statistics
        correlation = np.corrcoef(data['ln_val_bps'], data['energy_val_bps'])[0, 1]
        n_cells = len(data)
        
        # Count how many cells perform better with each model
        energy_better = (data['energy_val_bps'] > data['ln_val_bps']).sum()
        ln_better = (data['ln_val_bps'] > data['energy_val_bps']).sum()
        
        stats_text = f'N cells = {n_cells}\nCorrelation = {correlation:.3f}\n'
        stats_text += f'Energy better: {energy_better}\nLN better: {ln_better}'
        
        ax.text(0.05, 0.95, stats_text, transform=ax.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        # Add grid and legend
        ax.grid(True, alpha=0.3)
        ax.legend()
        
        # Make axes equal for better comparison
        ax.set_aspect('equal', adjustable='box')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Plot saved to {save_path}")
        
        return fig
    
    def plot_improvement_comparison(self, data: pd.DataFrame,
                                  save_path: Optional[str] = None,
                                  figsize: Tuple[int, int] = (12, 6)) -> plt.Figure:
        """
        Plot comparison of improvement from initial to final validation BPS.
        
        Args:
            data: DataFrame with comparison data
            save_path: Optional path to save the figure
            figsize: Figure size tuple
            
        Returns:
            matplotlib Figure object
        """
        if data.empty:
            raise ValueError("No data provided for plotting")
        
        # Calculate improvements
        data = data.copy()
        data['ln_improvement'] = data['ln_val_bps'] - data['ln_initial_val_bps']
        data['energy_improvement'] = data['energy_val_bps'] - data['energy_initial_val_bps']
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=figsize)
        
        # Plot 1: Improvement comparison scatter
        ax1.scatter(data['ln_improvement'], data['energy_improvement'], 
                   alpha=0.7, s=50, edgecolors='black', linewidth=0.5)
        
        # Add diagonal line
        min_val = min(data['ln_improvement'].min(), data['energy_improvement'].min())
        max_val = max(data['ln_improvement'].max(), data['energy_improvement'].max())
        ax1.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, linewidth=2, label='y=x')
        
        ax1.set_xlabel('LN Model Improvement (Final - Initial BPS)')
        ax1.set_ylabel('Energy Model Improvement (Final - Initial BPS)')
        ax1.set_title('Training Improvement Comparison')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        ax1.set_aspect('equal', adjustable='box')
        
        # Plot 2: Histogram of improvements
        ax2.hist(data['ln_improvement'], alpha=0.5, bins=20, label='LN Model', density=True)
        ax2.hist(data['energy_improvement'], alpha=0.5, bins=20, label='Energy Model', density=True)
        ax2.set_xlabel('Improvement (Final - Initial BPS)')
        ax2.set_ylabel('Density')
        ax2.set_title('Distribution of Training Improvements')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Improvement comparison plot saved to {save_path}")
        
        return fig
    
    def generate_summary_report(self, data: pd.DataFrame) -> str:
        """
        Generate a text summary report of the comparison.
        
        Args:
            data: DataFrame with comparison data
            
        Returns:
            String containing the summary report
        """
        if data.empty:
            return "No data available for summary."
        
        report = "=" * 50 + "\n"
        report += "STANDARD MODEL COMPARISON SUMMARY\n"
        report += "=" * 50 + "\n\n"
        
        # Overall statistics
        report += f"Total number of cells analyzed: {len(data)}\n"
        report += f"Number of sessions: {data['session'].nunique()}\n"
        report += f"Sessions: {', '.join(data['session'].unique())}\n\n"
        
        # Performance comparison
        energy_better = (data['energy_val_bps'] > data['ln_val_bps']).sum()
        ln_better = (data['ln_val_bps'] > data['energy_val_bps']).sum()
        
        report += "PERFORMANCE COMPARISON:\n"
        report += f"Cells where Energy model performs better: {energy_better} ({energy_better/len(data)*100:.1f}%)\n"
        report += f"Cells where LN model performs better: {ln_better} ({ln_better/len(data)*100:.1f}%)\n"
        
        # Statistical summary
        report += "\nVALIDATION BPS STATISTICS:\n"
        report += f"LN Model - Mean: {data['ln_val_bps'].mean():.4f}, Std: {data['ln_val_bps'].std():.4f}\n"
        report += f"Energy Model - Mean: {data['energy_val_bps'].mean():.4f}, Std: {data['energy_val_bps'].std():.4f}\n"
        
        correlation = np.corrcoef(data['ln_val_bps'], data['energy_val_bps'])[0, 1]
        report += f"Correlation between models: {correlation:.4f}\n\n"
        
        # Best and worst performers
        data_sorted = data.copy()
        data_sorted['energy_advantage'] = data_sorted['energy_val_bps'] - data_sorted['ln_val_bps']
        
        report += "BEST ENERGY MODEL PERFORMERS (Energy - LN BPS):\n"
        top_energy = data_sorted.nlargest(5, 'energy_advantage')
        for _, row in top_energy.iterrows():
            report += f"  Cell {row['cell_id']} ({row['session']}): +{row['energy_advantage']:.4f}\n"
        
        report += "\nBEST LN MODEL PERFORMERS (LN - Energy BPS):\n"
        top_ln = data_sorted.nsmallest(5, 'energy_advantage')
        for _, row in top_ln.iterrows():
            report += f"  Cell {row['cell_id']} ({row['session']}): {-row['energy_advantage']:.4f}\n"
        
        return report

def main():
    """
    Main function to run the analysis.
    """
    # Initialize analyzer
    analyzer = StandardModelAnalyzer()
    
    # Get available sessions
    print("Finding available sessions...")
    available_sessions = analyzer.get_available_sessions()
    print(f"Found {len(available_sessions)} sessions: {available_sessions}")
    
    if not available_sessions:
        print("No sessions found. Please check the base directory.")
        return
    
    # Load data for all sessions
    print("Loading comparison data...")
    all_data = analyzer.load_all_sessions_data(available_sessions)
    
    if all_data.empty:
        print("No comparison data found. Please check that both LN and energy results exist.")
        return
    
    print(f"Loaded data for {len(all_data)} cells across {all_data['session'].nunique()} sessions")
    
    # Generate and print summary report
    print("\n" + analyzer.generate_summary_report(all_data))
    
    # Create plots
    print("\nCreating comparison plots...")
    
    # Overall comparison plot
    fig1 = analyzer.plot_model_comparison(all_data, save_path="ln_vs_energy_comparison.png")
    plt.show()
    
    # Improvement comparison plot
    fig2 = analyzer.plot_improvement_comparison(all_data, save_path="improvement_comparison.png")
    plt.show()
    
    # Individual session plots
    for session in all_data['session'].unique():
        session_data = all_data[all_data['session'] == session]
        if len(session_data) > 5:  # Only plot if we have enough data points
            fig = analyzer.plot_model_comparison(
                session_data, 
                session_name=session,
                save_path=f"ln_vs_energy_{session}.png"
            )
            plt.show()
    
    print("\nAnalysis complete!")

if __name__ == "__main__":
    main()
#%%